import { create } from 'zustand'
import { persist } from 'zustand/middleware'

type SelectedUsersStore = {
  selectedUserIds: number[]
  toggleUserId: (id: number) => void
  clearSelected: () => void
}

export const useSelectedUsers = create<SelectedUsersStore>()(
  persist(
    (set, get) => ({
      selectedUserIds: [],
      toggleUserId: (id: number) => {
        const current = get().selectedUserIds
        const isSelected = current.includes(id)

        set({
          selectedUserIds: isSelected
            ? current.filter((userId) => userId !== id)
            : [...current, id],
        })
      },
      clearSelected: () => set({ selectedUserIds: [] }),
    }),
    {
      name: 'selected-user-ids',
    }
  )
)
