'use client'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  StepperI<PERSON>,
} from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import Cookies from 'universal-cookie'

import { cookieSettings } from '@/utils/cookie-settings'

import { CancelNewStudyPlanAlert } from './components/cancel-alert'
import { Step1 } from './step1'
import { CombinedCheckoutSchema, CombinedCheckoutType } from './validations'

export default function CriarTrilha() {
  const [activeStep, setActiveStep] = useState(0)
  const cookies = new Cookies(null, { path: '/' })

  const methods = useForm<CombinedCheckoutType>({
    resolver: zodResolver(CombinedCheckoutSchema),
  })

  const handleBackStep = () => {
    if (activeStep === 0) return
    setActiveStep(activeStep - 1)
  }

  const handleSubmit = (data: CombinedCheckoutType) => {
    if (activeStep === 0 && !!methods.formState.errors) {
      cookies.set('step1', JSON.stringify(data), cookieSettings())
      setActiveStep(1)
      return
    }
    console.log(data)
  }

  return (
    <FormProvider {...methods}>
      <form
        className="m-auto flex w-full flex-col gap-8 px-2 py-4 lg:p-4"
        onSubmit={methods.handleSubmit(handleSubmit)}
      >
        <div>
          <h1 className="text-ctx-content-title ts-heading-md">
            Trilha de aprendizagem
          </h1>
          <span className="text-ctx-content-base ts-paragraph-xs">
            Gerencie, acompanhe e analise o progresso de desenvolvimento dos
            colaboradores em tempo real.
          </span>
        </div>
        <div className="flex w-full justify-center">
          <Stepper activeStep={activeStep}>
            <StepperItem title="Informações" />
            <StepperItem title="Selecionar cursos" />
            <StepperItem title="Selecionar participantes" />
            <StepperItem title="Resumo" />
          </Stepper>
        </div>
        <div>
          <StepperContent activeStep={activeStep} index={0}>
            <Step1 />
          </StepperContent>
          <StepperContent activeStep={activeStep} index={1}>
            <p>Content 2</p>
          </StepperContent>
          <StepperContent activeStep={activeStep} index={2}>
            <p>Content 3</p>
          </StepperContent>
        </div>

        <div className="flex w-full justify-end gap-2">
          {activeStep === 0 ? (
            <CancelNewStudyPlanAlert />
          ) : (
            <Button type="button" hierarchy="tertiary" onClick={handleBackStep}>
              Voltar
            </Button>
          )}

          <Button type="submit">Próximo</Button>
        </div>
      </form>
    </FormProvider>
  )
}
