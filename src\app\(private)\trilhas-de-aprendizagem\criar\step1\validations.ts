import z from 'zod'

export const step1Schema = z
  .object({
    name: z.string().min(3, 'Esse campo é obrigatório'),
    description: z.string().optional(),
    isMandatory: z.boolean().optional(),
    duration: z.string().optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.isMandatory) {
        return data.duration
      }
      return true
    },
    {
      message: 'Esse campo é obrigatório',
      path: ['duration'],
    }
  )
  .refine(
    (data) => {
      if (data.isMandatory && data.duration === '4') {
        return data.startDate
      }
      return true
    },
    {
      message: 'Esse campo é obrigatório',
      path: ['startDate'],
    }
  )
  .refine(
    (data) => {
      if (data.isMandatory && data.duration === '4') {
        return data.endDate
      }
      return true
    },
    {
      message: 'Esse campo é obrigatório',
      path: ['endDate'],
    }
  )
  .refine(
    (data) => {
      if (data.isMandatory && data.duration === '4') {
        if (!data.startDate || !data.endDate) return false
        return data.startDate < data.endDate
      }
      return true
    },
    {
      message: 'A data de término deve ser maior que a data de início',
      path: ['endDate'],
    }
  )
