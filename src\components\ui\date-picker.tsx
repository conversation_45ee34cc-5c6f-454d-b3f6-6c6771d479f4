'use client'

import { But<PERSON> } from '@ads/components-react'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { Calendar as CalendarIcon } from 'lucide-react'
import * as React from 'react'

import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { cn } from '@/lib/utils'

type DatePickerProps = {
  value: Date
  onChange: (date: Date) => void
  errorMessage?: string
  placeholder?: string
  label?: string
  classNameButton?: string
}

export function DatePicker({
  value,
  onChange,
  errorMessage,
  label,
  placeholder = 'Selecione uma data',
  classNameButton,
}: DatePickerProps) {
  return (
    <Popover>
      <div className="flex h-20 flex-col items-start">
        {label && (
          <label className="mb-0.5 ts-paragraph-xxs" htmlFor={label}>
            {label}
          </label>
        )}
        <PopoverTrigger asChild>
          <Button
            hierarchy="secondary"
            data-empty={!value}
            className={cn(
              'flex justify-start whitespace-nowrap rounded-lg text-ctx-content-base ts-paragraph-xxs',
              classNameButton
            )}
            leadingIcon={CalendarIcon}
          >
            {value ? (
              format(value, 'PPP', { locale: ptBR })
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <span className="text-red-500 ts-subtitle-xxxs">{errorMessage}</span>
      </div>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          required={false}
          selected={value}
          onSelect={(date) => date && onChange(date)}
          locale={ptBR}
        />
      </PopoverContent>
    </Popover>
  )
}
