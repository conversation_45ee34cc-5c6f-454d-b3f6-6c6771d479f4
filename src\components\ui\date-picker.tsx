'use client'

import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { Calendar as CalendarIcon } from 'lucide-react'
import * as React from 'react'

import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

type DatePickerProps = {
  value: Date
  onChange: (date: Date) => void
  errorMessage?: string
  placeholder?: string
  label?: string
}

export function DatePicker({
  value,
  onChange,
  errorMessage,
  label,
  placeholder = 'Selecione uma data',
}: DatePickerProps) {
  return (
    <Popover>
      <div className="flex flex-col items-start gap-1">
        {label && (
          <label className="ts-paragraph-xxs" htmlFor={label}>
            {label}
          </label>
        )}
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            data-empty={!value}
            className="w-[280px] justify-start ts-paragraph-xxs data-[empty=true]:text-muted-foreground"
          >
            <CalendarIcon />
            {value ? (
              format(value, 'PPP', { locale: ptBR })
            ) : (
              <span>{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <span className="text-red-500 ts-subtitle-xxxs">{errorMessage}</span>
      </div>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          required={false}
          selected={value}
          onSelect={(date) => date && onChange(date)}
          locale={ptBR}
        />
      </PopoverContent>
    </Popover>
  )
}
