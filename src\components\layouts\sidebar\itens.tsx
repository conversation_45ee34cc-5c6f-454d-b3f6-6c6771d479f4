'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'

import { sidebarData } from './data'

export function NavMain() {
  const pathname = usePathname()

  return (
    <SidebarGroup>
      <SidebarMenu className="gap-2 p-2 md:px-4 md:pt-8">
        {sidebarData.map((item) => {
          return (
            <SidebarMenuItem key={item.name}>
              <SidebarMenuButton
                asChild
                isActive={pathname.startsWith(item.url)}
                tooltip={item.name}
                className="data-[active=true]:group-data-[collapsible=icon]:!h-10 data-[active=true]:group-data-[collapsible=icon]:!w-10 data-[active=true]:group-data-[collapsible=icon]:!p-2.5"
              >
                <Link href={item.url}>
                  {item.icon && <item.icon />}
                  <span className="ts-paragraph-xxs">{item.name}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
