'use client'

import { alert, But<PERSON>, TextField } from '@ads/components-react'
import axios from 'axios'
import { useRouter } from 'next/navigation'
import { useEffect, useMemo, useState } from 'react'
import { useMutation, useQuery, useQueryClient } from 'react-query'
import Cookies from 'universal-cookie'

import { createNewTeam, CreateNewTeamType } from '@/http/teams'
import { getB2bUsers } from '@/http/user'
import { useSelectedUsers } from '@/store/useSelectedUsersTeams'

import { TeamsSelectedTable } from '../../tables/teams/users-selected'
import { AddUsersModal } from './modal'

export function CreateTeam() {
  const { push } = useRouter()
  const queryClient = useQueryClient()

  const { companyId, enrollmentId } = useMemo(() => {
    const cookies = new Cookies(null, { path: '/' })
    return {
      companyId: cookies.get('b2bCompanyId'),
      enrollmentId: Number(cookies.get('b2bEnrollmentId')),
    }
  }, [])

  const { selectedUserIds, clearSelected } = useSelectedUsers()

  const [teamName, setTeamName] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [error, setError] = useState(false)

  const perPage = 10

  const { data: usersResponse, isLoading } = useQuery({
    queryKey: ['getb2bUsersSelectedGql', selectedUserIds],
    queryFn: () =>
      getB2bUsers({
        page: 1,
        limit: 9999,
        orderBy: 'NAME',
        company_id: companyId,
        enrollment_id: enrollmentId,
        hasSquad: false,
      }),
    keepPreviousData: true,
    staleTime: 1000 * 60 * 10, // 10 minutos
  })

  const allUsers = usersResponse?.users.data ?? []

  const filteredUsers = useMemo(
    () => allUsers.filter((user) => selectedUserIds.includes(user.id)),
    [allUsers, selectedUserIds]
  )

  const paginatedUsers = useMemo(
    () =>
      filteredUsers.slice((currentPage - 1) * perPage, currentPage * perPage),
    [filteredUsers, currentPage, perPage]
  )

  const usersByCompanySelected = useMemo(
    () => ({
      data: paginatedUsers,
      total: filteredUsers.length,
      perPage,
    }),
    [paginatedUsers, filteredUsers.length, perPage]
  )

  const backPage = () => {
    clearSelected()
    setTeamName('')
    push('/equipes')
  }

  const { mutate: createTeamMutation } = useMutation(
    (data: CreateNewTeamType) => createNewTeam(data),
    {
      onSuccess: () => {
        alert({
          alertType: 'success',
          title: 'Equipe criada com sucesso',
          description: 'A equipe foi criada com sucesso.',
        })
        queryClient.invalidateQueries(['teams'])
        backPage()
      },
      onError: (err) => {
        if (axios.isAxiosError(err)) {
          alert({
            alertType: 'danger',
            title: 'Erro ao criar equipe',
            description: err.response?.data.message || 'Erro desconhecido',
          })
        } else {
          alert({
            alertType: 'danger',
            title: 'Erro ao criar equipe',
            description:
              'Ocorreu um erro ao criar a equipe. Tente novamente mais tarde.',
          })
        }
      },
    }
  )

  const handleCreateNewTeam = () => {
    if (!teamName) {
      alert({
        alertType: 'danger',
        title: 'Equipe sem nome',
        description: 'Por favor, informe o nome da equipe.',
      })
      setError(true)
      return
    }

    setError(false)

    createTeamMutation({
      title: teamName,
      company_id: companyId,
      user_ids: selectedUserIds,
    })
  }

  useEffect(() => {
    const totalPages = Math.ceil(filteredUsers.length / perPage)
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages)
    }
  }, [filteredUsers.length, currentPage, perPage])

  return (
    <section>
      <div className="mb-8">
        <h1 className="text-ctx- mb-2 ts-heading-md">Equipes</h1>
        <p className="text-ctx-content-base ts-paragraph-xs">
          Gerencie, credencie e edite as equipes.
        </p>
      </div>

      <div className="rounded-2xl bg-ctx-layout-body p-4 md:p-8">
        <div className="flex flex-col items-end justify-between gap-8 rounded-2xl border border-solid border-ctx-layout-border p-4 md:flex-row">
          <TextField
            label="Nome"
            value={teamName}
            onChange={(e) => setTeamName(e.target.value)}
            hasError={error}
            size="md"
            placeholder="Digite o nome da Equipe"
            fullWidth
            custom={{
              input: 'bg-ctx-interactive-secondary',
            }}
          />

          <AddUsersModal />
        </div>

        <div className="mt-6 w-full">
          <TeamsSelectedTable
            isLoading={isLoading}
            isFetchingNewPage={false}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            usersByCompany={usersByCompanySelected}
          />
        </div>
      </div>

      <div className="flex justify-end gap-8 py-6">
        <Button onClick={backPage} hierarchy="tertiary" size="md">
          Cancelar
        </Button>
        <Button onClick={handleCreateNewTeam} size="md">
          Salvar
        </Button>
      </div>
    </section>
  )
}
