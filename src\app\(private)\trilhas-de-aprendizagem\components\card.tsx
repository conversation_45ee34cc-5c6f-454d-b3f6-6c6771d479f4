import { Tag, Tooltip } from '@ads/components-react'
import { Calendar } from 'lucide-react'
import { HiOutlineSparkles } from 'react-icons/hi2'

import { DropdownOptions } from '@/components/tables/study-plan/dropdown-options'
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { StudyPlanData } from '@/model/study-plan'
import { convertDateToCalendar } from '@/utils/convert-date'

type CardInfoProps = {
  studyPlanDataInfo: StudyPlanData
}

export function CardInfo({ studyPlanDataInfo }: CardInfoProps) {
  const handleTagLabel = (studyPlan: StudyPlanData) => {
     const isActive = studyPlan.status
     const hasEndDate = studyPlan.end_date
     const isExpired = new Date(studyPlan.end_date as Date) < new Date()

     
    if (
      studyPlan.status &&
      studyPlan.end_date &&
      new Date(studyPlan.end_date) > new Date()
    ) {
      return 'Finalizado'
    }

    if (studyPlan.end_date && new Date(studyPlan.end_date) < new Date()) {
      return 'Finalizado'
    }

    return 'Inativo'
  }

  const handleHandleTag = (studyPlan: StudyPlanData) => {
    const isActive = studyPlan.status
    const hasEndDate = studyPlan.end_date
    const isExpired = new Date(studyPlan.end_date as Date) < new Date()

    //Ativo – Toda trilha criada, com ou sem data de término, recebe o status Ativo.
    //Finalizado – Toda trilha que possuir data de término deve, automaticamente após essa data, receber o status Finalizado.
    //Arquivado – Toda trilha arquivada deve ser marcada como Arquivada e não será exibida na listagem padrão, aparecendo apenas ao aplicar o filtro Arquivado ou por meio da busca.

    if (isActive && isExpired) {
      return 'positive'
    }

    if (studyPlan.end_date && new Date(studyPlan.end_date)) {
      return 'positive'
    }

    return 'default'
  }

  const finishedPercetage =
    (((studyPlanDataInfo.users_completed_count as number) /
      studyPlanDataInfo.usersCount) as number) * 100

  console.log(studyPlanDataInfo)

  return (
    <Card className="flex h-[250px] w-full flex-col justify-between bg-ctx-layout-surface px-4 py-4 md:w-[334px]">
      <CardHeader className="p-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Tag
              label={handleTagLabel(studyPlanDataInfo)}
              status={handleHandleTag(studyPlanDataInfo)}
            />
            {studyPlanDataInfo.ai_generated && (
              <Tooltip title="Trilha criada com IA">
                <HiOutlineSparkles
                  className="text-ctx-interactive-primary"
                  size={20}
                />
              </Tooltip>
            )}
          </div>
          <DropdownOptions studyPlan={studyPlanDataInfo} />
        </div>
        <CardTitle className="line-clamp-2 text-ctx-content-title ts-subtitle-xs">
          {studyPlanDataInfo.name}
        </CardTitle>
        {studyPlanDataInfo.end_date && (
          <CardDescription className="mt-1 flex items-center gap-1 text-ctx-content-base ts-subtitle-xxxs">
            <Calendar size={16} />
            Vencimento:{' '}
            {convertDateToCalendar(
              new Date(studyPlanDataInfo.end_date as Date)
            )}
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className="m-0 flex gap-14 p-0">
        <div className="flex flex-col gap-2">
          <h3 className="text-ctx-content-title ts-subtitle-xs">Cursos</h3>
          <span className="text-ctx-content-base ts-subtitle-xxs">
            {studyPlanDataInfo.coursesCount}
          </span>
        </div>
        <div className="flex flex-col gap-2">
          <h3 className="text-ctx-content-title ts-subtitle-xs">Equipes</h3>
          <span className="text-ctx-content-base ts-subtitle-xxs">
            {studyPlanDataInfo.squadsCount}
          </span>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col items-start gap-2 p-0">
        <p className="text-ctx-content-title ts-subtitle-xs">
          Concluintes: {studyPlanDataInfo.users_completed_count} /{' '}
          {studyPlanDataInfo.usersCount}
        </p>
        <div className="flex w-full items-center gap-2">
          <Progress value={finishedPercetage} className="flex-1" />
          <span className="w-8 whitespace-nowrap text-end text-ctx-content-base ts-subtitle-xxs">
            {finishedPercetage} %
          </span>
        </div>
      </CardFooter>
    </Card>
  )
}
