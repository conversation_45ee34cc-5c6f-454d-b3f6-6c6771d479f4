import { StudyPlanData } from '@/model/study-plan'

export type StudyPlanStatus = 'ACTIVE' | 'INACTIVE' | 'FINISHED'
export type TagVariant = 'positive' | 'highlight' | 'default'

export const STUDY_PLAN_STATUS_CONFIG: Record<
  StudyPlanStatus,
  { label: string; variant: TagVariant }
> = {
  ACTIVE: { label: 'Ativo', variant: 'highlight' },
  INACTIVE: { label: 'Inativo', variant: 'default' },
  FINISHED: { label: 'Finalizado', variant: 'positive' },
}

export function getStudyPlanStatus(studyPlan: StudyPlanData): StudyPlanStatus {
  const isActive = studyPlan.status
  const isExpired = studyPlan.end_date
    ? new Date(studyPlan.end_date as Date) < new Date()
    : false

  if (isActive && isExpired) return 'FINISHED'
  if (isActive && !isExpired) return 'ACTIVE'
  return 'INACTIVE'
}

export function getStudyPlanStatusLabel(studyPlan: StudyPlanData): string {
  const status = getStudyPlanStatus(studyPlan)
  return STUDY_PLAN_STATUS_CONFIG[status].label
}

export function getStudyPlanStatusVariant(
  studyPlan: StudyPlanData
): TagVariant {
  const status = getStudyPlanStatus(studyPlan)
  return STUDY_PLAN_STATUS_CONFIG[status].variant
}

export function calculateCompletionPercentage(
  completed: number,
  total: number
): number {
  if (!total || total === 0) return 0
  const percentage = (completed / total) * 100
  return Math.round(percentage * 100) / 100
}
