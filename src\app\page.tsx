'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import Image from 'next/image'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { GoArrowRight } from 'react-icons/go'
import { HiOutlineEnvelope } from 'react-icons/hi2'

import InputForm from '@/components/ui/inputField'
import { useAuth } from '@/contexts/AuthContext'
import { LoginValidationSchema, LoginValidationType } from '@/validations/login'

export default function Home() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginValidationType>({
    resolver: zodResolver(LoginValidationSchema),
    mode: 'onChange',
  })

  const { signIn } = useAuth()

  async function handleSignIn(data: LoginValidationType) {
    await signIn(data)
  }

  return (
    <section className="bg-[url('/assets/solutionbackgroundlogin.webp')] bg-cover bg-top">
      <div className="flex h-screen w-full items-center justify-center px-4">
        <div className="flex w-full max-w-[592px] flex-col">
          <form
            className="rounded-md bg-base-gray-800 p-8"
            onSubmit={handleSubmit(handleSignIn)}
          >
            <div className="relative mx-auto mb-8 h-12 w-36">
              <Image src={'/assets/logo.webp'} alt="Plataforma Solution" fill />
            </div>

            <h1 className="mb-6 text-ctx-content-titleAlternative ts-subtitle-md">
              Acessar área
            </h1>

            <div className="mb-12 flex flex-col gap-6">
              <InputForm
                id="email"
                type="email"
                placeholder="Insira seu e-mail"
                label={'E-mail'}
                errorMessage={errors.email?.message}
                isAutoComplete
                leadingIcon={HiOutlineEnvelope}
                {...register('email')}
              />
              <InputForm
                id="password"
                type="password"
                placeholder="Insira sua senha"
                label="Senha"
                errorMessage={errors.password?.message}
                {...register('password')}
              />
            </div>

            <Button
              hierarchy="primary"
              size="lg"
              type="submit"
              trailingIcon={GoArrowRight}
              isLoading={isSubmitting}
              disabled={isSubmitting}
              className="mb-4"
              fullWidth
            >
              Próximo
            </Button>

            <Link
              className="flex justify-center"
              href={`${process.env.NEXT_PUBLIC_REDIRECT_URL_LMS}esqueci-senha?return_url=${process.env.NEXT_PUBLIC_REDIRECT_URL_B2B}`}
              target="_blank"
            >
              <LinkButton hierarchy="primary" size="md" onColor>
                Esqueceu a senha?
              </LinkButton>
            </Link>
          </form>
        </div>
      </div>
    </section>
  )
}
